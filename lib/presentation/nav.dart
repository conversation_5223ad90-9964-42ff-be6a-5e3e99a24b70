import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:langda/presentation/pages/mobile/auth_page/login_page_widget.dart';
import 'package:langda/presentation/pages/mobile/auth_page/nickname_setting/nickname_setting_page.dart';
import 'package:langda/presentation/pages/mobile/auth_page/sign_up_page_widget.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:langda/presentation/pages/mobile/home_page/card_page/card_detail_page.dart';
import 'package:langda/presentation/pages/mobile/home_page/diary_page/diary_main_page.dart';
import 'package:langda/presentation/pages/mobile/home_page/my_page/profile_page/profile_page.dart';
import 'package:langda/presentation/pages/mobile/update_required_page.dart';
import 'package:langda/presentation/router_constants.dart';
import 'package:langda/presentation/services/app_state_service.dart';
import 'package:langda/providers/process_diary_provider.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:provider/provider.dart';

import '../backend/model/card_model.dart';
import '../common/notification_box.dart';
import '../common/streak_review_dialog.dart';
import 'pages/mobile/home_page/card_page/card_list_page_widget.dart';
import 'pages/mobile/home_page/diary_page/diary_create_page.dart';
import 'pages/mobile/home_page/diary_page/diary_detail_page.dart';
import 'pages/mobile/home_page/home_nav_bar.dart';
import 'pages/mobile/home_page/my_page/profile_page/create_profile_page_widget.dart';
import 'pages/mobile/home_page/my_page/setting_page/setting_page.dart';
import 'route_redirector.dart';
import 'services/auth_service.dart';
import 'tablet/home_nav_list_bar.dart';

final routeRedirectorProvider = Provider<RouteRedirector>(
  create: (context) {
    final auth = Provider.of<AuthService>(context, listen: false);
    final appStateService =
        Provider.of<AppStateService>(context, listen: false);
    return RouteRedirector(
      auth,
      appStateService,
    );
  },
);

GoRouter createRouter(
    AuthService authService, AppStateService appStateService) {
  final listenables = <Listenable>[
    authService,
    appStateService,
  ];

  final _rootNavigatorKey = GlobalKey<NavigatorState>();
  final _shellNavigatorKeyHome =
      GlobalKey<NavigatorState>(debugLabel: 'shellHome');
  final _shellNavigatorKeyCard =
      GlobalKey<NavigatorState>(debugLabel: 'shellCard');
  final _shellNavigatorKeyProfile =
      GlobalKey<NavigatorState>(debugLabel: 'shellAuth');

  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: RoutePaths.home,
    observers: [PosthogObserver()],
    routes: [
      StatefulShellRoute.indexedStack(
        builder: (_, state, navShell) => ShellStack(
          navigationShell: navShell,
        ),
        branches: [
          // Home Branch (Index 0)
          StatefulShellBranch(
            navigatorKey: _shellNavigatorKeyHome,
            routes: [
              GoRoute(
                name: 'home',
                path: RoutePaths.home,
                builder: (context, __) => DiaryMainPage(
                  changePage: (navigationShellIndex) {
                    if (navigationShellIndex == 0) {
                      context.go(RoutePaths.home);
                    } else if (navigationShellIndex == 1) {
                      context.go(RoutePaths.card);
                    } else if (navigationShellIndex == 2) {
                      context.go(RoutePaths.auth);
                    }
                  },
                ),
              ),
            ],
          ),

          // Card Branch (Index 1 - Updated)
          StatefulShellBranch(
            navigatorKey: _shellNavigatorKeyCard,
            routes: [
              GoRoute(
                name: 'card list',
                path: RoutePaths.card,
                builder: (_, __) => CardListPageWidget(),
              ),
            ],
          ),

          // Auth Branch (Index 2 - Updated)
          StatefulShellBranch(
            navigatorKey: _shellNavigatorKeyProfile,
            routes: [
              GoRoute(
                path: RoutePaths.auth,
                builder: (_, __) => const SizedBox.shrink(),
                routes: [
                  GoRoute(
                    name: 'login',
                    path: RoutePaths.login,
                    builder: (_, __) => LoginPageWidget(),
                  ),
                  GoRoute(
                    name: 'signup',
                    path: RoutePaths.signup,
                    builder: (_, __) => SignUpPageWidget(),
                  ),
                  GoRoute(
                    name: 'create account',
                    path: RoutePaths.createAccount,
                    builder: (_, __) => CreateProfilePageWidget(),
                  ),
                ],
              ),
              GoRoute(
                path: RoutePaths.user,
                builder: (_, __) => const SizedBox.shrink(),
                routes: [
                  GoRoute(
                    name: 'profile',
                    path: RoutePaths.profile,
                    builder: (_, __) => ProfilePageWidget(),
                  ),
                  GoRoute(
                    name: 'settings',
                    path: RoutePaths.settings,
                    builder: (_, __) => SettingPageWidget(),
                  ),
                  GoRoute(
                    name: 'nickname edit',
                    path: RoutePaths.nicknameEdit,
                    builder: (_, __) => NicknameSettingPage(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: RoutePaths.diary,
        builder: (_, __) => const SizedBox.shrink(),
        routes: [
          GoRoute(
            name: 'create diary',
            path: RoutePaths.diaryCreate,
            builder: (_, __) => DiaryCreatePageWidget(),
          ),
          GoRoute(
            name: 'diary detail',
            path: RoutePaths.diaryDetail,
            builder: (_, state) {
              final entryId = state.pathParameters['entryId'] ?? 'unknown';

              return DiaryDetailPageWidget(
                entryId: entryId,
              );
            },
          ),
        ],
      ),
      GoRoute(
        name: 'card detail',
        path: RoutePaths.cardDetail,
        builder: (_, __) {
          final data = __.extra as Map<String, dynamic>?;
          final card = data?['card'] as CardModel?;
          final deepExplanationCost = data?['deepExplanationCost'] as int?;

          if (card == null || deepExplanationCost == null) {
            return const SizedBox.shrink();
          }
          return CardDetailPage(
            // card: card,
            deepExplanationCost: deepExplanationCost,
            correctionId: card.correctionId,
          );
        },
      ),
    ],
    refreshListenable: Listenable.merge(listenables),
    redirect: (context, state) {
      final redirector = context.read<RouteRedirector>();
      return redirector.redirect(context, state);
    },
    debugLogDiagnostics: true,
  );
}

class ShellStack extends StatelessWidget {
  const ShellStack({
    super.key,
    required this.navigationShell,
  });
  void _onItemTapped(int index, BuildContext context, AuthService authService) {
    switch (index) {
      case 0: // Home
        context.go(RoutePaths.home);
        break;
      case 1: // Card (Index updated)
        context.go(RoutePaths.card);
        break;
      case 2: // Auth (Index updated)
        context.go(
          authService.isLoggedIn
              ? RoutePaths.profilePagePath()
              : RoutePaths.loginPath(),
        );
        break;
    }
  }

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context) {
    final AppStateService appStateService = context.watch<AppStateService>();
    final DiaryProcessingProvider diaryProcessingProvider =
        context.watch<DiaryProcessingProvider>();
    final AuthService authService = context.watch<AuthService>();

    return SafeArea(
      top: false,
      bottom: false,
      child: Stack(
        children: [
          Scaffold(
            body: LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isMobile(context))
                      SizedBox(
                        width: constraints.maxWidth * 0.25,
                        height: constraints.maxHeight,
                        child: HomeNavListBar(
                          currentIndex: navigationShell.currentIndex,
                          onItemTapped: (p0) =>
                              _onItemTapped(p0, context, authService),
                        ),
                      ),
                    SizedBox(
                      width: isMobile(context)
                          ? constraints.maxWidth
                          : constraints.maxWidth * 0.75,
                      height: constraints.maxHeight,
                      child: navigationShell,
                    ),
                  ],
                );
              },
            ),
            bottomNavigationBar: (isMobile(context))
                ? HomeNavBar(
                    currentIndex: navigationShell.currentIndex,
                    onItemTapped: (p0) =>
                        _onItemTapped(p0, context, authService),
                  )
                : null,
          ),
          if (diaryProcessingProvider.status == ProcessingStatus.completed &&
              diaryProcessingProvider.status != ProcessingStatus.error)
            const NotificationBoxWidget(),
          if (showStreakReview &&
              diaryProcessingProvider.shouldShowStreakReview)
            StreakReviewDialog(
              onDismiss: () {
                diaryProcessingProvider.dismissStreakReview();
              },
            ),
          if (appStateService.updateState == UpdateState.updateRequired)
            const UpdateRequiredPage(),
          if (appStateService.isLoading)
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Colors.black54,
              ),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}

mixin ShellScreenStateMixin<T extends StatefulWidget> on State<T> {
  AuthService get authService => context.watch<AuthService>();
  AppStateService get appStateService => context.watch<AppStateService>();

  void logBuild(String screenName) {
    print('$screenName building with ShellScreenStateMixin.'
        'LoggedIn: ${authService.isLoggedIn}, Loading: ${appStateService.isLoading}');
  }
}
