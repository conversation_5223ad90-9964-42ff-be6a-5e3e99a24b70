import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

class HomeNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onItemTapped;
  const HomeNavBar(
      {super.key, required this.currentIndex, required this.onItemTapped});

  @override
  State<HomeNavBar> createState() => _HomeNavBarState();
}

class _HomeNavBarState extends State<HomeNavBar> {
  void _onItemTapped(int index) {
    setState(() {
      widget.onItemTapped(index);
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(
        maxHeight: 90,
      ),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFA8A8A8),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _navBarItemBuilder(
            iconPath: 'assets/images/icons/calendar_1.5-512.png',
            title: FlutterI18n.translate(context, 'navigation.diary'),
            index: 0,
          ),
          _navBarItemBuilder(
            iconPath: 'assets/images/icons/bookmark_1.5-512.png',
            title: FlutterI18n.translate(context, 'navigation.card'),
            index: 1,
          ),
          _navBarItemBuilder(
            iconPath: 'assets/images/icons/user-03_1.5-mg.png',
            title: FlutterI18n.translate(context, 'navigation.profile'),
            index: 2,
          ),
        ],
      ),
    );
  }

  Widget _navBarItemBuilder({
    required String iconPath,
    required String title,
    required int index,
  }) {
    return Semantics(
      label: title,
      child: GestureDetector(
        onTap: () {
          _onItemTapped(index);
        },
        behavior: HitTestBehavior.opaque,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 5,
              ),
              child: Image.asset(
                iconPath,
                width: 28,
                color: widget.currentIndex == index
                    ? LDColors.foundationLimeDark
                    : LDColors.darkGrey,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5.0),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: widget.currentIndex == index
                      ? LDColors.foundationLimeDark
                      : LDColors.darkGrey,
                  fontSize: 10.92,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
