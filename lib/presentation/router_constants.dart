class RoutePaths {
  static const String home = '/home';
  // Home sub-paths

  // Diary Tab (New)
  static const String diary = '/diary'; // New top-level path for the tab
  // Diary sub-paths (relative to diaryTab)
  static const String diaryCreate = 'create'; // Kept as relative
  static const String diaryDetail =
      ':entryId'; // Kept as relative with parameter

  static String diaryCreatePath() =>
      '$diary/$diaryCreate'; // e.g., /home/<USER>
  static String diaryDetailPath(String entryId) =>
      '$diary/$entryId'; // e.g., /home/<USER>

  // Card
  static const String card = '/card';
  // Card sub-paths
  // static const String cardDetail = ':correctionId';
  static const String cardDetail = '/card_detail'; // Kept as relative

  // Auth
  static const String auth = '/auth';
  // Auth sub-paths
  static const String login = 'login';
  static const String logout = 'logout';
  static const String signup = 'signup';

  static const String createAccount = 'create-account';

  // Auth path helper
  static String loginPath() => '$auth/$login';
  static String logoutPath() => '$auth/$logout';
  static String signupPath() => '$auth/$signup';

  static String createAccountPath() => '$auth/$createAccount';

  // User
  static const String user = '/user';
  // User sub-paths
  static const String profile = 'profile';
  static const String settings = 'settings';
  static const String nicknameEdit = 'nickname_edit';

  static String profilePagePath() => '$user/$profile';
  static String settingsPath() => '$user/$settings';
  static String nicknameEditPath() => '$user/$nicknameEdit';

  // Sign In with Social
  static const String socialLogin = '/sign-in-social';
  static const String magicLinkLogin = '/sign-in-magic-link';

  // Sign Up with Social
  static const String signupWithSocial = '/sign-up-social';
  static const String signupWithMagicLink = '/sign-up-magic-link';

  // Update Required
  static const String updateRequired = '/update-required';
}

class DeepLinkPaths {
  static const String signInMagicLink = 'sign-in-magic-link';
  static const String signInSocial = 'sign-in-social';
  static const String signUpSocial = 'sign-up-social';
  static const String signUpMagicLink = 'sign-up-magic-link';

  static const String scheme = 'com.oibori.langda';
}
